"use client";

import Image from "next/image";
import Link from "next/link";
import { Card, CardTitle } from "@/components/ui/card";
import { Github, Instagram, Youtube, MessageSquare } from "lucide-react";
import Tilt from "react-parallax-tilt";
import { ShineBorder } from "@/components/magicui/shine-border";
import dynamic from "next/dynamic";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { RiTiktokLine } from "react-icons/ri";
import { SiRoblox } from "react-icons/si";
import { cn } from "@/lib/utils";
import { Spotlight } from "@/components/ui/spotlight";
export default function Page() {
  const IconCloud = dynamic(() => import("@/components/ui/icon-cloud"), { ssr: false });
  const slugs = [
    "typescript",
    "javascript",
    "react",
    "html5",
    "css3",
    "nodedotjs",
    "express",
    "nextdotjs",
    "postgresql",
    "firebase",
    "nginx",
    "vercel",
    "docker",
    "git",
    "github",
    "gitlab",
    "nuxtdotjs",
    "tailwindcss",
    "vuedotjs",
  ];
  return (
    <>
      <TooltipProvider>
        <div
          className={cn(
            "pointer-events-none fixed inset-0 z-0 [background-size:40px_40px] select-none",
            "[background-image:linear-gradient(to_right,#171717_1px,transparent_1px),linear-gradient(to_bottom,#171717_1px,transparent_1px)]",
          )}
        />

        <Spotlight
          className="-top-40 left-0 md:-top-20 md:left-60"
          fill="white"
        />
        <div className="flex flex-col p-3 sm:p-6 space-y-6 max-w-[90%] sm:max-w-[620px] mx-auto">
          <div className="flex flex-col items-center justify-center w-full">
            <Tilt tiltMaxAngleX={5} tiltMaxAngleY={5} className="w-full" scale={1.02}>
              <Card className="relative p-4 md:p-6 rounded-lg w-full h-fit flex flex-col pt-6 bg-gradient-to-br from-[#2c2c2c] to-[#000000] mt-5">
                <ShineBorder shineColor="white" />
                <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4 md:gap-6 mb-2">
                  <div className="w-20 h-20 md:h-24 md:w-24 flex-shrink-0 relative rounded-full overflow-hidden">
                    <ShineBorder shineColor="white" borderWidth={2} />
                    <Image
                      width={80}
                      height={80}
                      src="/vani.png"
                      alt="avatar"
                      className="rounded-full object-cover w-full h-full"
                    />
                  </div>
                  <div className="flex flex-col items-center sm:items-start mt-2 sm:mt-0">
                    <h1 className="text-xl md:text-2xl font-semibold">Nguyen Dinh Bao (Vani)</h1>
                    <div className="flex flex-wrap mt-2 justify-center sm:justify-start gap-1">
                      <span className="px-2 py-1 text-xs font-medium rounded-md border bg-secondary/40 cursor-pointer transition-all hover:bg-secondary">
                        Full Stack Developer
                      </span>
                      <span className="px-2 py-1 text-xs font-medium rounded-md border bg-secondary/40 cursor-pointer transition hover:bg-secondary">
                        Content Creator
                      </span>
                      <span className="px-2 py-1 text-xs font-medium rounded-md border bg-secondary/40 cursor-pointer transition hover:bg-secondary">
                        Graphic Designer
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex justify-center sm:justify-end">
                  <div className="flex space-x-5">
                    <Tooltip>
                      <Link href="https://github.com/vanixjnk" className="transition-colors hover:text-primary" aria-label="Home">

                        <TooltipTrigger><Github className="w-5 h-5" /></TooltipTrigger>
                        <TooltipContent>
                          {/* <Link
                          href="https://github.com/zmh-program"
                          target="_blank"
                          className="transition-colors hover:text-primary"
                          aria-label="GitHub"
                        >
                          <Github className="w-5 h-5" />
                        </Link> */}
                          Github
                        </TooltipContent>
                      </Link>
                    </Tooltip>
                    <Tooltip>
                      <Link href="https://instagram.com/vanixjnk" className="transition-colors hover:text-primary" aria-label="Home">
                        <TooltipTrigger><Instagram className="w-5 h-5" /></TooltipTrigger>
                        <TooltipContent>
                          {/* <Link
                          href="https://github.com/zmh-program"
                          target="_blank"
                          className="transition-colors hover:text-primary"
                          aria-label="GitHub"
                        >
                          <Github className="w-5 h-5" />
                        </Link> */}
                          Instagram
                        </TooltipContent>
                      </Link>
                    </Tooltip>
                  </div>
                </div>
              </Card>
            </Tilt>
            <Tilt tiltMaxAngleX={5} tiltMaxAngleY={5} className="w-full" scale={1.02}>
              <Card className="relative w-full h-fit p-4 sm:p-6 bg-gradient-to-br from-[#2c2c2c] to-[#000000] mt-5">
                <ShineBorder shineColor="white" />
                <div className="relative flex flex-col items-center justify-center overflow-hidden rounded-lg border bg-background px-4 sm:px-10 py-8 mt-4">
                  <CardTitle className="text-center text-xl font-semibold mb-4">
                    About Me
                  </CardTitle>
                  <p className="text-sm text-muted-foreground text-center max-w-lg">
                    I am a Full Stack Developer with a passion for creating beautiful and functional web applications. I have experience in various technologies and frameworks, and I love to learn new things.
                  </p>
                </div>
              </Card>
            </Tilt>
            <Tilt tiltMaxAngleX={5} tiltMaxAngleY={5} className="w-full" scale={1.02}>
              <Card className="relative w-full h-fit p-4 sm:p-6 bg-gradient-to-br from-[#2c2c2c] to-[#000000] mt-5">
                <ShineBorder shineColor="white" />
                <div className="relative flex flex-col items-center justify-center overflow-hidden rounded-lg border bg-background px-4 sm:px-10 py-8 mt-4">
                  <CardTitle className="text-center text-xl font-semibold mb-4">
                    Connect With Me
                  </CardTitle>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 w-full max-w-md">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Link href="https://youtube.com/@vanixjnk" target="_blank" className="group">
                          <div className="flex items-center gap-3 p-3 rounded-md border transition-all hover:bg-secondary/40">
                            <div className="bg-red-600/20 p-2 rounded-full group-hover:bg-red-600/30 transition-all">
                              <Youtube className="w-5 h-5 text-red-500" />
                            </div>
                            <div>
                              <h3 className="text-sm font-medium">YouTube</h3>
                              <p className="text-xs text-muted-foreground">@vanixjnk</p>
                            </div>
                          </div>
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent>Subscribe to my channel</TooltipContent>
                    </Tooltip>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Link href="https://youtube.com/@vanixjnk2" target="_blank" className="group">
                          <div className="flex items-center gap-3 p-3 rounded-md border transition-all hover:bg-secondary/40">
                            <div className="bg-red-600/20 p-2 rounded-full group-hover:bg-red-600/30 transition-all">
                              <Youtube className="w-5 h-5 text-red-500" />
                            </div>
                            <div>
                              <h3 className="text-sm font-medium">YouTube</h3>
                              <p className="text-xs text-muted-foreground">@vanixjnk2</p>
                            </div>
                          </div>
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent>Subscribe to my channel</TooltipContent>
                    </Tooltip>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Link href="https://tiktok.com/@vanixjnk" target="_blank" className="group">
                          <div className="flex items-center gap-3 p-3 rounded-md border transition-all hover:bg-secondary/40">
                            <div className="bg-black/20 p-2 rounded-full group-hover:bg-black/30 transition-all">
                              <RiTiktokLine className="w-5 h-5 text-white" />
                            </div>
                            <div>
                              <h3 className="text-sm font-medium">TikTok</h3>
                              <p className="text-xs text-muted-foreground">@vanixjnk</p>
                            </div>
                          </div>
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent>Follow me on TikTok</TooltipContent>
                    </Tooltip>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Link href="https://instagram.com/vanixjnk" target="_blank" className="group">
                          <div className="flex items-center gap-3 p-3 rounded-md border transition-all hover:bg-secondary/40">
                            <div className="bg-pink-600/20 p-2 rounded-full group-hover:bg-pink-600/30 transition-all">
                              <Instagram className="w-5 h-5 text-pink-500" />
                            </div>
                            <div>
                              <h3 className="text-sm font-medium">Instagram</h3>
                              <p className="text-xs text-muted-foreground">@vanixjnk</p>
                            </div>
                          </div>
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent>Follow me on Instagram</TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Link href="https://linkedin.com/in/vanixjnk" target="_blank" className="group">
                          <div className="flex items-center gap-3 p-3 rounded-md border transition-all hover:bg-secondary/40">
                            <div className="bg-blue-600/20 p-2 rounded-full group-hover:bg-blue-600/30 transition-all">
                              <svg className="w-5 h-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                                <rect x="2" y="9" width="4" height="12"></rect>
                                <circle cx="4" cy="4" r="2"></circle>
                              </svg>
                            </div>
                            <div>
                              <h3 className="text-sm font-medium">LinkedIn</h3>
                              <p className="text-xs text-muted-foreground">Professional Profile</p>
                            </div>
                          </div>
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent>Connect on LinkedIn</TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Link href="https://facebook.com/vanixjnk" target="_blank" className="group">
                          <div className="flex items-center gap-3 p-3 rounded-md border transition-all hover:bg-secondary/40">
                            <div className="bg-blue-700/20 p-2 rounded-full group-hover:bg-blue-700/30 transition-all">
                              <svg className="w-5 h-5 text-blue-700" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                              </svg>
                            </div>
                            <div>
                              <h3 className="text-sm font-medium">Facebook</h3>
                              <p className="text-xs text-muted-foreground">Stay Connected</p>
                            </div>
                          </div>
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent>Follow me on Facebook</TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Link href="https://discord.gg/d4D858N6pu" target="_blank" className="group">
                          <div className="flex items-center gap-3 p-3 rounded-md border transition-all hover:bg-secondary/40">
                            <div className="bg-indigo-600/20 p-2 rounded-full group-hover:bg-indigo-600/30 transition-all">
                              <MessageSquare className="w-5 h-5 text-indigo-500" />
                            </div>
                            <div>
                              <h3 className="text-sm font-medium">Discord</h3>
                              <p className="text-xs text-muted-foreground">Join Community</p>
                            </div>
                          </div>
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent>Join my Discord server</TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Link href="https://www.roblox.com/users/400399334/profile" target="_blank" className="group">
                          <div className="flex items-center gap-3 p-3 rounded-md border transition-all hover:bg-secondary/40">
                            <div className="bg-red-600/20 p-2 rounded-full group-hover:bg-red-600/30 transition-all">
                              <SiRoblox className="w-5 h-5 text-red-500" />
                            </div>
                            <div>
                              <h3 className="text-sm font-medium">Roblox</h3>
                              <p className="text-xs text-muted-foreground">Play Together</p>
                            </div>
                          </div>
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent>Find me on Roblox</TooltipContent>
                    </Tooltip>
                  </div>
                </div>
              </Card>
            </Tilt>
            <Tilt tiltMaxAngleX={5} tiltMaxAngleY={5} className="w-full" scale={1.02}>
              <Card className="relative w-full h-fit p-4 sm:p-6 bg-gradient-to-br from-[#2c2c2c] to-[#000000] mt-5">
                <ShineBorder shineColor="white" />
                <div className="relative flex size-full max-w-full sm:max-w-lg items-center justify-center overflow-hidden rounded-lg border bg-background px-4 sm:px-20 pb-12 sm:pb-20 pt-8 mt-4">
                  <IconCloud iconSlugs={slugs} />
                </div>
              </Card>
            </Tilt>
            <div className="footer py-6 px-4 text-center text-sm">
              <div>© Copyright 2025 © Designed by Vani. All rights reserved.</div>
            </div>
          </div>
        </div>
      </TooltipProvider>
    </>
  );
}