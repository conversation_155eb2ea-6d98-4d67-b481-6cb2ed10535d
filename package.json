{"name": "vanixjnkdev", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-tooltip": "^1.2.4", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cobe": "^0.6.3", "lucide-react": "^0.507.0", "motion": "^12.9.4", "next": "15.3.1", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icon-cloud": "^4.1.7", "react-icons": "^5.5.0", "react-parallax-tilt": "^1.7.294", "tailwind-merge": "^3.2.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}